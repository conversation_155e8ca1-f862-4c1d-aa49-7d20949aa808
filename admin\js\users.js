// Configuration
const API_BASE_URL = 'http://localhost:5000/api';

// Global variables
let adminToken = null;
let users = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    checkAdminAuth();
    setupEventListeners();
});

// Authentication
function checkAdminAuth() {
    adminToken = localStorage.getItem('admin_token');
    if (!adminToken) {
        showLoginModal();
    } else {
        loadUsers();
    }
}

function showLoginModal() {
    document.getElementById('login-modal').style.display = 'block';
}

function hideLoginModal() {
    document.getElementById('login-modal').style.display = 'none';
}

async function adminLogin(username, password) {
    try {
        const response = await fetch(`${API_BASE_URL}/admin/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (response.ok) {
            adminToken = data.token;
            localStorage.setItem('admin_token', adminToken);
            hideLoginModal();
            loadUsers();
            return true;
        } else {
            alert(data.error || 'Login failed');
            return false;
        }
    } catch (error) {
        console.error('Login error:', error);
        alert('Login failed. Please try again.');
        return false;
    }
}

function logout() {
    adminToken = null;
    localStorage.removeItem('admin_token');
    showLoginModal();
}

// Users management
async function loadUsers() {
    const loadingElement = document.getElementById('loading-users');
    const containerElement = document.getElementById('users-container');
    const noUsersElement = document.getElementById('no-users');

    try {
        loadingElement.style.display = 'block';
        containerElement.style.display = 'none';
        noUsersElement.style.display = 'none';

        const response = await fetch(`${API_BASE_URL}/admin/users`, {
            headers: {
                'Authorization': `Bearer ${adminToken}`
            }
        });

        if (!response.ok) {
            if (response.status === 403) {
                logout();
                return;
            }
            throw new Error('Failed to load users');
        }

        const data = await response.json();
        users = data.users || [];

        loadingElement.style.display = 'none';

        if (users.length === 0) {
            noUsersElement.style.display = 'block';
        } else {
            containerElement.style.display = 'block';
            displayUsers();
        }
    } catch (error) {
        console.error('Error loading users:', error);
        loadingElement.style.display = 'none';
        containerElement.innerHTML = '<div class="error">Failed to load users. Please try again later.</div>';
    }
}

function displayUsers() {
    const listElement = document.getElementById('users-list');
    listElement.innerHTML = '';

    users.forEach(user => {
        const userRow = createUserRow(user);
        listElement.appendChild(userRow);
    });
}

function createUserRow(user) {
    const row = document.createElement('div');
    row.className = 'table-row';
    
    const joinDate = new Date(user.created_at).toLocaleDateString();
    const status = user.is_active ? 'Active' : 'Inactive';
    const statusClass = user.is_active ? 'status-open' : 'status-closed';
    
    row.innerHTML = `
        <div class="user-id">${user._id.slice(-8)}</div>
        <div class="full-name">${user.full_name || 'N/A'}</div>
        <div class="in-game-name">${user.in_game_name || 'N/A'}</div>
        <div class="email">${user.email || 'N/A'}</div>
        <div class="points"><strong>${user.points || 0}</strong></div>
        <div class="predictions">${user.total_predictions || 0}</div>
        <div class="join-date">${joinDate}</div>
        <div class="status">
            <span class="match-status ${statusClass}">${status}</span>
        </div>
    `;

    return row;
}

// Event listeners
function setupEventListeners() {
    // Login form
    document.getElementById('login-submit').addEventListener('click', async function(e) {
        e.preventDefault();
        const username = document.getElementById('admin-username').value;
        const password = document.getElementById('admin-password').value;
        
        if (username && password) {
            await adminLogin(username, password);
        }
    });

    // Enter key for login
    document.getElementById('admin-password').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('login-submit').click();
        }
    });

    // Logout button
    document.getElementById('logout-btn').addEventListener('click', logout);

    // Refresh button
    document.getElementById('refresh-users-btn').addEventListener('click', loadUsers);

    // Auto-refresh every 60 seconds
    setInterval(() => {
        if (adminToken) {
            loadUsers();
        }
    }, 60000);
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString();
}
