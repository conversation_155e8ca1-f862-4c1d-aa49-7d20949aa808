from pymongo import MongoClient
from datetime import datetime, timedelta
from bson import ObjectId
import os
import hashlib
import re

class Database:
    def __init__(self):
        self.client = None
        self.db = None
    
    def init_app(self, app):
        """Initialize database connection"""
        mongodb_uri = os.environ.get('MONGODB_URI', 'mongodb://localhost:27017/')
        db_name = os.environ.get('DB_NAME', 'valorant_predictions')
        
        self.client = MongoClient(mongodb_uri)
        self.db = self.client[db_name]
        
        # Create indexes for better performance (with error handling)
        try:
            self.db.matches.create_index("status")
            self.db.predictions.create_index([("user_id", 1), ("match_id", 1)], unique=True)
            self.db.users.create_index([("points", -1)])  # For leaderboard sorting
            self.db.users.create_index("email", unique=True)  # Unique email constraint
            print("Database indexes created successfully")
        except Exception as e:
            print(f"Warning: Could not create indexes: {e}")
            # Continue without indexes - they're not critical for basic functionality

db = Database()

class Match:
    @staticmethod
    def create_match(team_a, team_b, prediction_duration_minutes=5):
        """Create a new match"""
        match_doc = {
            'teamA': team_a,
            'teamB': team_b,
            'created_at': datetime.utcnow(),
            'prediction_duration_minutes': prediction_duration_minutes,
            'status': 'created',  # created, open, closed, finished
            'start_time': None,
            'prediction_end_time': None,
            'winner': None,
            'total_predictions': 0,
            'predictions_teamA': 0,
            'predictions_teamB': 0
        }
        
        result = db.db.matches.insert_one(match_doc)
        return str(result.inserted_id)
    
    @staticmethod
    def get_match_by_id(match_id):
        """Get a match by its ID"""
        try:
            match = db.db.matches.find_one({'_id': ObjectId(match_id)})
            if match:
                match['_id'] = str(match['_id'])
            return match
        except:
            return None
    
    @staticmethod
    def get_all_matches():
        """Get all matches for public view"""
        matches = list(db.db.matches.find().sort('created_at', -1))
        for match in matches:
            match['_id'] = str(match['_id'])
            # Don't expose sensitive admin data
            if 'total_predictions' in match:
                del match['total_predictions']
            if 'predictions_teamA' in match:
                del match['predictions_teamA']
            if 'predictions_teamB' in match:
                del match['predictions_teamB']
        return matches
    
    @staticmethod
    def get_all_matches_admin():
        """Get all matches with admin details"""
        matches = list(db.db.matches.find().sort('created_at', -1))
        for match in matches:
            match['_id'] = str(match['_id'])
        return matches
    
    @staticmethod
    def start_prediction(match_id):
        """Start the prediction window for a match"""
        try:
            match = Match.get_match_by_id(match_id)
            if not match or match['status'] != 'created':
                return False
            
            start_time = datetime.utcnow()
            end_time = start_time + timedelta(minutes=match['prediction_duration_minutes'])
            
            result = db.db.matches.update_one(
                {'_id': ObjectId(match_id)},
                {
                    '$set': {
                        'status': 'open',
                        'start_time': start_time,
                        'prediction_end_time': end_time
                    }
                }
            )
            
            return result.modified_count > 0
        except:
            return False
    
    @staticmethod
    def close_prediction(match_id):
        """Close the prediction window for a match"""
        try:
            result = db.db.matches.update_one(
                {'_id': ObjectId(match_id), 'status': 'open'},
                {'$set': {'status': 'closed'}}
            )
            return result.modified_count > 0
        except:
            return False
    
    @staticmethod
    def set_winner_and_award_points(match_id, winner_team):
        """Set the winner and award points to correct predictions"""
        try:
            # Validate winner team
            match = Match.get_match_by_id(match_id)
            if not match:
                raise ValueError("Match not found")
            
            if winner_team not in [match['teamA'], match['teamB']]:
                raise ValueError("Invalid winner team")
            
            # Update match with winner
            db.db.matches.update_one(
                {'_id': ObjectId(match_id)},
                {
                    '$set': {
                        'winner': winner_team,
                        'status': 'finished'
                    }
                }
            )
            
            # Find all correct predictions
            correct_predictions = list(db.db.predictions.find({
                'match_id': match_id,
                'selected_team': winner_team
            }))

            print(f"Found {len(correct_predictions)} correct predictions for match {match_id}")

            # Award points to users with correct predictions
            points_awarded = 0
            for prediction in correct_predictions:
                print(f"Processing prediction: {prediction}")

                # Mark prediction as correct
                pred_result = db.db.predictions.update_one(
                    {'_id': prediction['_id']},
                    {'$set': {'is_correct': True}}
                )
                print(f"Prediction update result: modified_count={pred_result.modified_count}")

                # Award points to user (1 point per correct prediction)
                points_success = User.add_points(prediction['user_id'], 1)
                if points_success:
                    points_awarded += 1
                    print(f"Successfully awarded points to user {prediction['user_id']}")
                else:
                    print(f"Failed to award points to user {prediction['user_id']}")
            
            # Mark incorrect predictions
            db.db.predictions.update_many(
                {
                    'match_id': match_id,
                    'selected_team': {'$ne': winner_team}
                },
                {'$set': {'is_correct': False}}
            )
            
            return points_awarded
            
        except Exception as e:
            raise e

class Prediction:
    @staticmethod
    def create_prediction(user_id, match_id, selected_team):
        """Create a new prediction"""
        prediction_doc = {
            'user_id': user_id,
            'match_id': match_id,
            'selected_team': selected_team,
            'timestamp': datetime.utcnow(),
            'is_correct': None  # Will be set when match is finished
        }
        
        result = db.db.predictions.insert_one(prediction_doc)
        
        # Update match prediction counts
        if selected_team:
            match = Match.get_match_by_id(match_id)
            if match:
                update_field = 'predictions_teamA' if selected_team == match['teamA'] else 'predictions_teamB'
                db.db.matches.update_one(
                    {'_id': ObjectId(match_id)},
                    {
                        '$inc': {
                            'total_predictions': 1,
                            update_field: 1
                        }
                    }
                )
        
        return str(result.inserted_id)
    
    @staticmethod
    def get_user_prediction(user_id, match_id):
        """Check if user already has a prediction for this match"""
        return db.db.predictions.find_one({
            'user_id': user_id,
            'match_id': match_id
        })
    
    @staticmethod
    def get_user_predictions(user_id):
        """Get all predictions for a user"""
        predictions = list(db.db.predictions.find({'user_id': user_id}).sort('timestamp', -1))
        
        # Enrich with match data
        for prediction in predictions:
            prediction['_id'] = str(prediction['_id'])
            match = Match.get_match_by_id(prediction['match_id'])
            if match:
                prediction['match'] = {
                    'teamA': match['teamA'],
                    'teamB': match['teamB'],
                    'status': match['status'],
                    'winner': match.get('winner')
                }
        
        return predictions

class User:
    @staticmethod
    def hash_password(password):
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()

    @staticmethod
    def validate_email(email):
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    @staticmethod
    def register_user(full_name, email, in_game_name, password):
        """Register a new user"""
        try:
            # Validate email format
            if not User.validate_email(email):
                return {'success': False, 'error': 'Invalid email format'}

            # Check if email already exists
            if db.db.users.find_one({'email': email}):
                return {'success': False, 'error': 'Email already registered'}

            # Check if in-game name already exists
            if db.db.users.find_one({'in_game_name': in_game_name}):
                return {'success': False, 'error': 'In-game name already taken'}

            # Create user document
            user_doc = {
                'full_name': full_name,
                'email': email,
                'in_game_name': in_game_name,
                'password_hash': User.hash_password(password),
                'points': 0,
                'total_predictions': 0,
                'correct_predictions': 0,
                'created_at': datetime.utcnow(),
                'is_active': True
            }

            result = db.db.users.insert_one(user_doc)
            user_id = str(result.inserted_id)

            return {
                'success': True,
                'user_id': user_id,
                'user': {
                    'id': user_id,
                    'full_name': full_name,
                    'email': email,
                    'in_game_name': in_game_name,
                    'points': 0
                }
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    @staticmethod
    def authenticate_user(email, password):
        """Authenticate user login"""
        try:
            user = db.db.users.find_one({'email': email, 'is_active': True})
            if not user:
                return {'success': False, 'error': 'Invalid email or password'}

            # Check password
            if user['password_hash'] != User.hash_password(password):
                return {'success': False, 'error': 'Invalid email or password'}

            return {
                'success': True,
                'user': {
                    'id': str(user['_id']),
                    'full_name': user['full_name'],
                    'email': user['email'],
                    'in_game_name': user['in_game_name'],
                    'points': user['points']
                }
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    @staticmethod
    def ensure_user_exists(user_id):
        """Ensure user exists in database (legacy support)"""
        try:
            existing_user = db.db.users.find_one({'_id': ObjectId(user_id)})
            if not existing_user:
                print(f"User {user_id} not found, creating legacy user")
                # This is for backward compatibility with old user IDs
                user_doc = {
                    'full_name': f'User {user_id[:8]}',
                    'email': f'user{user_id[:8]}@temp.com',
                    'in_game_name': f'Player{user_id[:8]}',
                    'password_hash': '',
                    'points': 0,
                    'total_predictions': 0,
                    'correct_predictions': 0,
                    'created_at': datetime.utcnow(),
                    'is_active': True,
                    'is_legacy': True
                }
                result = db.db.users.insert_one(user_doc)
                print(f"Created legacy user {user_id}: {result.inserted_id}")
            else:
                print(f"User {user_id} exists: {existing_user.get('in_game_name', 'No name')}")
        except Exception as e:
            print(f"Error ensuring user exists {user_id}: {e}")
            raise e

    @staticmethod
    def get_user_by_id(user_id):
        """Get user by ID"""
        try:
            user = db.db.users.find_one({'_id': ObjectId(user_id)})
            if not user:
                return None

            # Return user in the same format as authenticate_user
            return {
                'id': str(user['_id']),
                'full_name': user['full_name'],
                'email': user['email'],
                'in_game_name': user['in_game_name'],
                'points': user['points'],
                'total_predictions': user.get('total_predictions', 0),
                'correct_predictions': user.get('correct_predictions', 0),
                'created_at': user.get('created_at'),
                'is_active': user.get('is_active', True)
            }
        except Exception as e:
            print(f"Error getting user by ID {user_id}: {e}")
            return None

    @staticmethod
    def add_points(user_id, points):
        """Add points to a user"""
        try:
            User.ensure_user_exists(user_id)
            result = db.db.users.update_one(
                {'_id': ObjectId(user_id)},
                {
                    '$inc': {
                        'points': points,
                        'correct_predictions': 1
                    }
                }
            )
            print(f"Points update result for user {user_id}: modified_count={result.modified_count}")
            return result.modified_count > 0
        except Exception as e:
            print(f"Error adding points to user {user_id}: {e}")
            return False

    @staticmethod
    def get_leaderboard(limit=50):
        """Get the global leaderboard"""
        users = list(db.db.users.find({'is_active': True}).sort('points', -1).limit(limit))

        # Calculate accuracy for each user
        for user in users:
            user_id = str(user['_id'])
            total_predictions = db.db.predictions.count_documents({'user_id': user_id})
            user['total_predictions'] = total_predictions
            user['accuracy'] = (user['correct_predictions'] / total_predictions * 100) if total_predictions > 0 else 0
            user['_id'] = user_id

            # Use in_game_name for display
            user['display_name'] = user.get('in_game_name', user.get('full_name', f'User {user_id[:8]}'))

        return users

    @staticmethod
    def get_all_users():
        """Get all users for admin panel"""
        users = list(db.db.users.find().sort('created_at', -1))
        for user in users:
            user['_id'] = str(user['_id'])
            # Calculate total predictions
            user['total_predictions'] = db.db.predictions.count_documents({'user_id': user['_id']})
        return users
