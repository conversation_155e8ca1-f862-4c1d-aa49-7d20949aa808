// Shared utilities for Valorant Tournament Predictions
// This file provides common functions and utilities used across all pages

// Configuration
const API_BASE_URL = 'http://localhost:5000/api';

// Global user data management
class UserDataManager {
    constructor() {
        this.currentUser = null;
        this.eventListeners = new Set();
        this.init();
    }

    init() {
        // Listen for storage changes from other tabs
        window.addEventListener('storage', (e) => {
            if (e.key === 'valorant_user' && e.newValue) {
                console.log('UserDataManager: User data updated in another tab');
                try {
                    const newUser = JSON.parse(e.newValue);
                    this.updateUser(newUser, 'storage');
                } catch (error) {
                    console.error('UserDataManager: Error parsing updated user data:', error);
                }
            }
        });

        // Listen for custom user update events
        window.addEventListener('userPointsUpdated', (e) => {
            console.log('UserDataManager: Points update event received:', e.detail);
            if (this.currentUser && e.detail.userId === this.currentUser.id) {
                this.currentUser.points = e.detail.points;
                this.notifyListeners('pointsUpdated', e.detail);
            }
        });
    }

    // Load user from localStorage
    loadUser() {
        const savedUser = localStorage.getItem('valorant_user');
        if (savedUser) {
            try {
                this.currentUser = JSON.parse(savedUser);
                return this.currentUser;
            } catch (e) {
                console.error('UserDataManager: Invalid user data in localStorage');
                localStorage.removeItem('valorant_user');
                return null;
            }
        }
        return null;
    }

    // Update user data and notify listeners
    updateUser(userData, source = 'api') {
        const oldPoints = this.currentUser ? this.currentUser.points : 0;
        this.currentUser = userData;
        
        // Save to localStorage
        localStorage.setItem('valorant_user', JSON.stringify(userData));
        
        // Notify listeners
        this.notifyListeners('userUpdated', {
            user: userData,
            oldPoints: oldPoints,
            newPoints: userData.points,
            source: source
        });

        // Dispatch global event for backward compatibility
        if (source !== 'event') {
            window.dispatchEvent(new CustomEvent('userPointsUpdated', {
                detail: { 
                    userId: userData.id,
                    points: userData.points,
                    user: userData 
                }
            }));
        }
    }

    // Update only points
    updatePoints(newPoints, source = 'api') {
        if (this.currentUser) {
            const oldPoints = this.currentUser.points;
            this.currentUser.points = newPoints;
            
            // Save to localStorage
            localStorage.setItem('valorant_user', JSON.stringify(this.currentUser));
            
            // Notify listeners
            this.notifyListeners('pointsUpdated', {
                userId: this.currentUser.id,
                oldPoints: oldPoints,
                newPoints: newPoints,
                user: this.currentUser,
                source: source
            });

            // Dispatch global event
            if (source !== 'event') {
                window.dispatchEvent(new CustomEvent('userPointsUpdated', {
                    detail: { 
                        userId: this.currentUser.id,
                        points: newPoints,
                        user: this.currentUser 
                    }
                }));
            }
        }
    }

    // Add event listener
    addEventListener(eventType, callback) {
        const listener = { eventType, callback };
        this.eventListeners.add(listener);
        return listener;
    }

    // Remove event listener
    removeEventListener(listener) {
        this.eventListeners.delete(listener);
    }

    // Notify all listeners
    notifyListeners(eventType, data) {
        this.eventListeners.forEach(listener => {
            if (listener.eventType === eventType || listener.eventType === 'all') {
                try {
                    listener.callback(data);
                } catch (error) {
                    console.error('UserDataManager: Error in event listener:', error);
                }
            }
        });
    }

    // Get current user
    getUser() {
        return this.currentUser;
    }

    // Clear user data
    clearUser() {
        this.currentUser = null;
        localStorage.removeItem('valorant_user');
        this.notifyListeners('userCleared', {});
    }
}

// Create global instance
window.userDataManager = new UserDataManager();

// Utility function to update user display elements
function updateUserDisplayElements(user) {
    if (!user) {
        // Clear display elements
        const pointsElement = document.getElementById('user-points');
        const userIdElement = document.getElementById('user-id-display');
        
        if (pointsElement) pointsElement.textContent = 'Points: 0';
        if (userIdElement) userIdElement.textContent = '';
        return;
    }

    const points = typeof user.points === 'number' ? user.points : 0;
    const pointsText = `Points: ${points}`;
    const userText = user.in_game_name || user.full_name || 'Unknown User';

    console.log('updateUserDisplayElements: Setting points to:', pointsText);
    console.log('updateUserDisplayElements: Setting user to:', userText);

    // Update points display
    const pointsElement = document.getElementById('user-points');
    if (pointsElement) {
        pointsElement.textContent = pointsText;
        pointsElement.innerHTML = pointsText;
        
        // Visual feedback for update
        pointsElement.style.opacity = '0.7';
        setTimeout(() => {
            pointsElement.style.opacity = '1';
        }, 100);
    }

    // Update user display
    const userIdElement = document.getElementById('user-id-display');
    if (userIdElement) {
        userIdElement.textContent = userText;
    }

    // Show/hide logout and refresh buttons
    const logoutBtn = document.getElementById('nav-logout-btn');
    const refreshBtn = document.getElementById('refresh-points-btn');

    if (logoutBtn) {
        logoutBtn.style.display = user ? 'inline-block' : 'none';
    }

    if (refreshBtn) {
        refreshBtn.style.display = user ? 'inline-block' : 'none';
    }
}

// Utility function to fetch user stats from API
async function fetchUserStats(userId) {
    try {
        console.log(`fetchUserStats: Loading stats for user: ${userId}`);
        
        const response = await fetch(`${API_BASE_URL}/user/${userId}`);
        console.log('fetchUserStats: Response status:', response.status);

        if (response.ok) {
            const data = await response.json();
            console.log('fetchUserStats: Response data:', data);
            return data.user;
        } else {
            console.error('fetchUserStats: Failed to load user stats:', response.status);
            return null;
        }
    } catch (error) {
        console.error('fetchUserStats: Error loading user stats:', error);
        return null;
    }
}

// Utility function to refresh user points
async function refreshUserPoints() {
    const user = window.userDataManager.getUser();
    if (!user) {
        console.log('refreshUserPoints: No current user');
        return false;
    }

    const freshUserData = await fetchUserStats(user.id);
    if (freshUserData) {
        console.log(`refreshUserPoints: Points updated from ${user.points} to ${freshUserData.points}`);
        window.userDataManager.updateUser(freshUserData, 'refresh');
        return true;
    }
    return false;
}

// Debug function to test point synchronization
function testPointsSynchronization() {
    console.log('=== TESTING POINTS SYNCHRONIZATION ===');
    const user = window.userDataManager.getUser();
    if (!user) {
        console.error('No user logged in for testing');
        return;
    }

    console.log('Current user:', user);
    console.log('Current points:', user.points);

    // Simulate a points update
    const newPoints = user.points + 10;
    console.log(`Simulating points update from ${user.points} to ${newPoints}`);

    window.userDataManager.updatePoints(newPoints, 'test');

    // Check if all displays are updated
    setTimeout(() => {
        const pointsElement = document.getElementById('user-points');
        if (pointsElement) {
            console.log('Header points display:', pointsElement.textContent);
            console.log('Expected:', `Points: ${newPoints}`);
            console.log('Match:', pointsElement.textContent === `Points: ${newPoints}`);
        } else {
            console.log('Points element not found on this page');
        }
    }, 100);
}

// Export for use in other files
window.sharedUtils = {
    UserDataManager,
    updateUserDisplayElements,
    fetchUserStats,
    refreshUserPoints,
    testPointsSynchronization,
    API_BASE_URL
};

// Make test function available globally for debugging
window.testPointsSynchronization = testPointsSynchronization;
