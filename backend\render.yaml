services:
  - type: web
    name: valorant-prediction-backend
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: gunicorn app:app
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: SECRET_KEY
        generateValue: true
      - key: ADMIN_TOKEN
        generateValue: true
      - key: MONGODB_URI
        sync: false  # You'll need to set this manually in Render dashboard
      - key: DB_NAME
        value: valorant_predictions
