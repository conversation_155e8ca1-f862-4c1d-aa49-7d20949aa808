# MongoDB Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/
DB_NAME=valorant_predictions

# Security
SECRET_KEY=your-secret-key-here
ADMIN_TOKEN=your-secure-admin-token-here

# Server Configuration
PORT=5000

# Instructions:
# 1. Copy this file to .env
# 2. Replace the placeholder values with your actual configuration
# 3. Never commit the .env file to version control
# 4. For production deployment on Render.com, set these as environment variables in the dashboard
