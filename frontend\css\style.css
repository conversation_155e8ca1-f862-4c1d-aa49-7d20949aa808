/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background-color: #0F1419;
    color: #FFFFFF;
    line-height: 1.6;
    overflow-x: hidden;
}

/* ===== VARIABLES ===== */
:root {
    --primary-red: #FF4655;
    --dark-bg: #0F1419;
    --card-bg: #1E2328;
    --border-color: #3C3C41;
    --text-primary: #FFFFFF;
    --text-secondary: #AAABAD;
    --accent-blue: #00D4FF;
    --success-green: #00F5A0;
    --warning-yellow: #FFCC02;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.accent {
    color: var(--primary-red);
}

/* ===== LAYOUT ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.mobile-container {
    padding: 0 16px;
    margin-bottom: 80px; /* Space for bottom nav */
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary-red);
}

/* ===== NAVIGATION ===== */
.navbar {
    background: rgba(15, 20, 25, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 1px solid var(--border-color);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
}

.nav-logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
}

.nav-logo .accent {
    color: var(--primary-red);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-red);
    background: rgba(255, 70, 85, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-secondary {
    background: rgba(108, 117, 125, 0.8);
    color: white;
    border: 1px solid rgba(108, 117, 125, 0.8);
}

.btn-secondary:hover {
    background: rgba(108, 117, 125, 1);
    border-color: rgba(108, 117, 125, 1);
    transform: translateY(-1px);
}

/* ===== MOBILE APP-STYLE NAVIGATION ===== */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 44px;
    height: 44px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.hamburger-line {
    width: 24px;
    height: 3px;
    background: var(--text-primary);
    margin: 2px 0;
    transition: all 0.3s ease;
    border-radius: 2px;
}

/* ===== BOTTOM NAVIGATION BAR ===== */
.mobile-bottom-nav {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--card-bg);
    border-top: 1px solid var(--border-color);
    z-index: 1000;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    padding: env(safe-area-inset-bottom, 0) 0 0 0;
}

.bottom-nav-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 8px 0 12px 0;
    max-width: 100%;
}

.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    text-decoration: none;
    color: var(--text-secondary);
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-width: 60px;
    border-radius: 12px;
}

.nav-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    margin-bottom: 4px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 8px;
}

.nav-icon i {
    font-size: 18px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-label {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%) scaleX(0);
    width: 24px;
    height: 3px;
    background: var(--primary-red);
    border-radius: 2px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bottom-nav-item.active {
    color: var(--primary-red);
}

.bottom-nav-item.active .nav-icon {
    background: rgba(255, 70, 85, 0.15);
    transform: scale(1.1);
}

.bottom-nav-item.active .nav-icon i {
    color: var(--primary-red);
    transform: scale(1.1);
}

.bottom-nav-item.active .nav-indicator {
    transform: translateX(-50%) scaleX(1);
}

.bottom-nav-item:active {
    transform: scale(0.95);
}

/* ===== PULL TO REFRESH ===== */
.pull-to-refresh {
    position: fixed;
    top: -80px;
    left: 0;
    right: 0;
    height: 80px;
    background: var(--card-bg);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 999;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid var(--border-color);
}

.pull-to-refresh.active {
    transform: translateY(80px);
}

.refresh-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-red);
    border-radius: 50%;
    margin-bottom: 4px;
    transition: transform 0.3s ease;
}

.refresh-icon i {
    color: white;
    font-size: 14px;
}

.refresh-icon.spinning i {
    animation: spin 1s linear infinite;
}

.refresh-text {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* ===== PREDICTION STATUS BAR ===== */
.prediction-status-bar {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, var(--primary-red), #e63946);
    color: white;
    z-index: 999;
    box-shadow: 0 2px 10px rgba(255, 70, 85, 0.3);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.status-bar-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-timer {
    font-family: 'Courier New', monospace;
    font-size: 1.2rem;
    font-weight: 700;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* ===== MAIN CONTENT ===== */
.main-content {
    margin-top: 70px;
    min-height: calc(100vh - 70px);
    background: var(--dark-bg);
    padding: 2rem 0;
}

.main-content.with-status-bar {
    margin-top: 130px; /* 70px navbar + 60px status bar */
}

/* ===== HERO SECTION ===== */
.hero {
    text-align: center;
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--dark-bg) 0%, var(--card-bg) 100%);
    position: relative;
    margin-bottom: 3rem;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23FF4655" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 2px;
}

.hero h1 .accent {
    color: var(--primary-red);
}

.hero p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 2rem;
}

.hero-actions {
    margin-top: 2rem;
}

.btn-youtube {
    background: linear-gradient(135deg, var(--primary-red), #e63946);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3);
}

.btn-youtube:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 70, 85, 0.4);
    background: linear-gradient(135deg, #e63946, #d32f2f);
}

.btn-youtube i {
    font-size: 1.2rem;
}

/* ===== USER SETUP ===== */
.user-setup {
    margin-bottom: 3rem;
}

.setup-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 3rem;
    max-width: 500px;
    margin: 0 auto;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.setup-card h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.input-group {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.input-group input {
    flex: 1;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    background: var(--dark-bg);
    color: var(--text-primary);
    font-family: 'Rajdhani', sans-serif;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-red);
}

.input-group input::placeholder {
    color: var(--text-secondary);
}

/* ===== AUTHENTICATION FORMS ===== */
.auth-form {
    width: 100%;
}

.auth-switch {
    text-align: center;
    margin-top: 1rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.auth-switch a {
    color: var(--primary-red);
    text-decoration: none;
    font-weight: 600;
}

.auth-switch a:hover {
    text-decoration: underline;
}

/* ===== BUTTONS ===== */
.btn {
    background: var(--primary-red);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    font-family: 'Rajdhani', sans-serif;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:hover {
    background: #e63946;
    transform: translateY(-2px);
}

.btn-primary {
    background: var(--primary-red);
}

.btn-primary:hover {
    background: #e63946;
}

.btn-secondary {
    background: var(--accent-blue);
    color: var(--dark-bg);
}

.btn-secondary:hover {
    background: #00b8d4;
}

.btn-success {
    background: var(--success-green);
    color: var(--dark-bg);
}

.btn-success:hover {
    background: #00d4aa;
}

.btn-danger {
    background: transparent;
    border: 1px solid var(--primary-red);
    color: var(--primary-red);
}

.btn-danger:hover {
    background: var(--primary-red);
    color: white;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* ===== MATCHES SECTION ===== */
.matches-section {
    padding: 3rem 0;
}

.matches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

/* ===== MOBILE MATCHES LAYOUT ===== */
.mobile-matches {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.mobile-refresh {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: var(--primary-red);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(255, 70, 85, 0.3);
}

.mobile-refresh:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(255, 70, 85, 0.4);
}

.mobile-refresh:active {
    transform: scale(0.95);
}

.mobile-refresh i {
    font-size: 16px;
}

.mobile-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 3rem 1rem;
    text-align: center;
}

.loading-spinner {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--primary-red);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.loading-spinner i {
    color: white;
    font-size: 20px;
}

.mobile-no-matches {
    text-align: center;
    padding: 3rem 1rem;
    background: var(--card-bg);
    border-radius: 16px;
    border: 1px solid var(--border-color);
}

.empty-state-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: rgba(255, 70, 85, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.empty-state-icon i {
    font-size: 24px;
    color: var(--primary-red);
}

.match-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    transition: transform 0.3s ease, border-color 0.3s ease;
    position: relative;
    overflow: hidden;
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-red);
}

.match-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-red);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.match-status {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: 1px solid;
}

.status-open {
    background: rgba(0, 245, 160, 0.2);
    color: var(--success-green);
    border-color: var(--success-green);
    animation: pulse 2s infinite;
}

.status-closed {
    background: rgba(255, 204, 2, 0.2);
    color: var(--warning-yellow);
    border-color: var(--warning-yellow);
}

.status-finished {
    background: rgba(0, 212, 255, 0.2);
    color: var(--accent-blue);
    border-color: var(--accent-blue);
}

.status-created {
    background: rgba(255, 70, 85, 0.2);
    color: var(--primary-red);
    border-color: var(--primary-red);
}

.status-closed {
    background: rgba(255, 204, 2, 0.2);
    color: var(--warning-yellow);
    border-color: var(--warning-yellow);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.teams {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 2rem 0;
    text-align: center;
}

.team {
    flex: 1;
}

.team-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

.vs {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-red);
    margin: 0 2rem;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.countdown {
    background: rgba(255, 70, 85, 0.1);
    border: 1px solid var(--primary-red);
    border-radius: 6px;
    padding: 1rem;
    text-align: center;
    margin: 1rem 0;
}

.countdown-timer {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-red);
    font-family: 'Courier New', monospace;
}

.predict-btn {
    background: var(--primary-red);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Rajdhani', sans-serif;
    width: 100%;
    margin-top: 0.5rem;
}

.predict-btn:hover {
    background: #e63946;
    transform: translateY(-2px);
}

.predict-btn:disabled {
    background: var(--border-color);
    color: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
}

.match-actions {
    margin-top: 1.5rem;
    text-align: center;
}

/* ===== LIVE MATCH STYLES ===== */
.live-indicator {
    text-align: center;
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(255, 70, 85, 0.1);
    border: 1px solid var(--primary-red);
    border-radius: 8px;
}

.live-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary-red);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
    animation: livePulse 2s infinite;
}

.live-badge i {
    font-size: 0.7rem;
    animation: liveBlink 1s infinite;
}

@keyframes livePulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes liveBlink {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.live-indicator p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
    font-weight: 500;
}

.btn-live {
    background: linear-gradient(135deg, var(--primary-red), #e63946);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3);
    animation: liveButtonPulse 3s infinite;
}

.btn-live:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 70, 85, 0.4);
    background: linear-gradient(135deg, #e63946, #d32f2f);
    text-decoration: none;
    color: white;
}

.btn-live i {
    font-size: 1.1rem;
}

@keyframes liveButtonPulse {
    0% { box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3); }
    50% { box-shadow: 0 4px 20px rgba(255, 70, 85, 0.5); }
    100% { box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3); }
}

/* ===== LOADING & EMPTY STATES ===== */
.loading {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.loading i {
    font-size: 3rem;
    color: var(--primary-red);
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-matches {
    text-align: center;
    padding: 4rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin: 2rem 0;
}

.no-matches i {
    font-size: 4rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-matches h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.no-matches p {
    color: var(--text-secondary);
}

/* ===== MODAL ===== */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    margin: 5% auto;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    overflow: hidden;
}

.modal-header {
    background: var(--dark-bg);
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    position: relative;
}

.modal-header h2 {
    color: var(--text-primary);
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.modal-body {
    padding: 2rem;
    color: var(--text-primary);
}

.close {
    color: var(--text-secondary);
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: var(--primary-red);
}

/* ===== MOBILE MODAL STYLES ===== */
.mobile-modal {
    backdrop-filter: blur(10px);
}

.mobile-modal-content {
    margin: 5% auto;
    width: 95%;
    max-width: 400px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
}

.mobile-modal-header {
    background: var(--dark-bg);
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.close-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 70, 85, 0.1);
    border: none;
    color: var(--primary-red);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 70, 85, 0.2);
    transform: scale(1.05);
}

.mobile-modal-body {
    padding: 1.5rem;
}

/* ===== QUICK ACTIONS GRID ===== */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 16px;
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    color: var(--text-primary);
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 80px;
}

.quick-action-btn:hover {
    background: rgba(255, 70, 85, 0.1);
    border-color: var(--primary-red);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.quick-action-btn:active {
    transform: translateY(0) scale(0.98);
}

.quick-action-btn i {
    font-size: 24px;
    margin-bottom: 8px;
    color: var(--primary-red);
}

.quick-action-btn span {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
}

.countdown-info {
    background: rgba(255, 70, 85, 0.1);
    border: 1px solid var(--primary-red);
    border-radius: 6px;
    padding: 1.5rem;
    text-align: center;
    margin-top: 2rem;
}

.countdown-info p {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

/* ===== FOOTER ===== */
footer {
    background: var(--card-bg);
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
    text-align: center;
    padding: 2rem 0;
    margin-top: 4rem;
}

footer p {
    font-size: 0.9rem;
    font-weight: 500;
}

/* ===== LEADERBOARD STYLES ===== */
.leaderboard-section {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem 0;
}

.leaderboard-container {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.leaderboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.leaderboard-header h2 {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.leaderboard-table {
    width: 100%;
}

.table-header,
.table-row {
    display: grid;
    grid-template-columns: 60px 1fr 100px 120px 100px;
    gap: 1rem;
    padding: 1rem;
    align-items: center;
}

.table-header {
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.table-row {
    background: rgba(30, 35, 40, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    color: var(--text-primary);
    font-weight: 500;
}

.table-row:hover {
    background: rgba(255, 70, 85, 0.1);
    border-color: var(--primary-red);
    transform: translateX(3px);
}

.table-row.current-user {
    background: rgba(255, 70, 85, 0.2);
    border-color: var(--primary-red);
}

.table-row.rank-1 {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
    border-color: #ffd700;
}

.table-row.rank-2 {
    background: linear-gradient(135deg, rgba(192, 192, 192, 0.2), rgba(192, 192, 192, 0.1));
    border-color: #c0c0c0;
}

.table-row.rank-3 {
    background: linear-gradient(135deg, rgba(205, 127, 50, 0.2), rgba(205, 127, 50, 0.1));
    border-color: #cd7f32;
}

.rank {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
}

.rank i.gold {
    color: #ffd700;
}

.rank i.silver {
    color: #c0c0c0;
}

.rank i.bronze {
    color: #cd7f32;
}

.you-badge {
    background: var(--primary-red);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    margin-left: 0.5rem;
}

.username {
    font-weight: 600;
}

.points strong {
    color: var(--primary-red);
    font-size: 1.1rem;
}

/* ===== PROFILE STYLES ===== */
.profile-section {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.profile-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    margin-bottom: 2rem;
}

.profile-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.profile-avatar {
    width: 80px;
    height: 80px;
    background: var(--primary-red);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    text-align: center;
    padding: 1.5rem;
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-red);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.predictions-history {
    margin-top: 2rem;
}

.predictions-history h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.prediction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 0.5rem;
    transition: border-color 0.3s ease;
}

.prediction-item:hover {
    border-color: var(--primary-red);
}

.prediction-match {
    flex: 1;
}

.prediction-result {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.result-correct {
    background: rgba(0, 245, 160, 0.2);
    color: var(--success-green);
    border: 1px solid var(--success-green);
}

.result-incorrect {
    background: rgba(255, 70, 85, 0.2);
    color: var(--primary-red);
    border: 1px solid var(--primary-red);
}

.result-pending {
    background: rgba(255, 204, 2, 0.2);
    color: var(--warning-yellow);
    border: 1px solid var(--warning-yellow);
}

/* ===== MOBILE-FIRST RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    /* Navigation */
    .nav-container {
        height: 70px;
        padding: 0 16px;
        justify-content: space-between;
    }

    .nav-menu {
        display: none; /* Hide desktop menu on mobile */
    }

    .mobile-menu-toggle {
        display: none; /* Hide hamburger on mobile, use bottom nav instead */
    }

    .mobile-bottom-nav {
        display: block; /* Show bottom navigation on mobile */
    }

    /* Main content adjustments */
    .main-content {
        margin-top: 70px;
        padding-bottom: 100px; /* Space for bottom nav */
    }

    .main-content.with-status-bar {
        margin-top: 130px;
    }

    /* Mobile-optimized containers */
    .container {
        padding: 0 16px;
    }

    .mobile-container {
        padding: 0 16px;
    }

    /* Hero section mobile optimization */
    .hero {
        padding: 2rem 0;
        margin-bottom: 2rem;
    }

    .hero h1 {
        font-size: 2.2rem;
        line-height: 1.2;
        margin-bottom: 1rem;
    }

    .hero p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    /* Mobile match cards */
    .matches-grid {
        display: none; /* Hide desktop grid */
    }

    .mobile-matches {
        display: flex; /* Show mobile layout */
    }

    .match-card {
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .match-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    }

    /* Mobile buttons */
    .btn {
        min-height: 48px;
        padding: 12px 24px;
        font-size: 16px;
        border-radius: 12px;
        font-weight: 600;
    }

    .btn-youtube {
        padding: 16px 24px;
        font-size: 16px;
        min-height: 52px;
        border-radius: 16px;
    }

    .btn-live {
        padding: 16px 24px;
        font-size: 16px;
        min-height: 52px;
        border-radius: 16px;
    }

    .predict-btn {
        min-height: 48px;
        font-size: 16px;
        border-radius: 12px;
        padding: 12px 20px;
    }

    /* Mobile forms */
    .input-group {
        flex-direction: column;
        margin-top: 16px;
    }

    .input-group input {
        padding: 16px;
        font-size: 16px;
        border-radius: 12px;
        min-height: 52px;
    }

    /* Mobile user info */
    .user-info {
        flex-direction: column;
        align-items: flex-end;
        gap: 8px;
    }

    .user-details {
        align-items: flex-end;
        text-align: right;
    }

    .user-actions {
        justify-content: flex-end;
        gap: 0.25rem;
    }

    .btn-sm {
        padding: 8px 16px;
        font-size: 14px;
        min-height: 40px;
        border-radius: 10px;
    }

    /* Mobile sections */
    .section-title {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
    }

    /* Mobile status bar */
    .status-bar-content {
        flex-direction: column;
        gap: 8px;
        text-align: center;
        padding: 12px 16px;
    }

    .status-message {
        font-size: 14px;
    }

    .status-timer {
        font-size: 16px;
        padding: 8px 16px;
    }

    /* Mobile live elements */
    .live-badge {
        font-size: 12px;
        padding: 6px 12px;
    }

    .live-indicator {
        margin: 16px 0;
        padding: 16px;
        border-radius: 12px;
    }

    /* Mobile modals */
    .modal-content {
        margin: 10% auto;
        width: 95%;
        max-width: 400px;
        border-radius: 20px;
    }

    .modal-header {
        padding: 20px;
    }

    .modal-body {
        padding: 20px;
    }

    /* Mobile teams layout */
    .teams {
        flex-direction: column;
        gap: 16px;
        margin: 20px 0;
    }

    .vs {
        margin: 0;
        font-size: 14px;
        padding: 8px 0;
    }
}

/* ===== TABLET STYLES ===== */
@media (min-width: 769px) and (max-width: 1024px) {
    .mobile-bottom-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .main-content {
        padding-bottom: 2rem;
    }

    .matches-grid {
        display: grid;
    }

    .mobile-matches {
        display: none;
    }
}

/* ===== EXTRA SMALL DEVICES ===== */
@media (max-width: 480px) {
    .hero h1 {
        font-size: 1.8rem;
        line-height: 1.1;
    }

    .section-title {
        font-size: 1.6rem;
    }

    .match-card {
        padding: 16px;
        border-radius: 12px;
    }

    .btn-youtube {
        padding: 14px 20px;
        font-size: 15px;
        min-height: 48px;
    }

    .btn-live {
        padding: 14px 20px;
        font-size: 15px;
        min-height: 48px;
    }

    .status-message {
        font-size: 13px;
    }

    .status-timer {
        font-size: 15px;
        padding: 6px 12px;
    }

    .bottom-nav-container {
        padding: 6px 0 10px 0;
    }

    .nav-icon {
        width: 28px;
        height: 28px;
    }

    .nav-icon i {
        font-size: 16px;
    }

    .nav-label {
        font-size: 10px;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .quick-action-btn {
        padding: 16px;
        min-height: 70px;
    }

    .mobile-modal-content {
        width: 98%;
        margin: 2% auto;
    }

    .container {
        padding: 0 12px;
    }

    .mobile-container {
        padding: 0 12px;
    }

    .live-badge {
        font-size: 11px;
        padding: 4px 8px;
    }
}

/* ===== LANDSCAPE MOBILE OPTIMIZATION ===== */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        padding: 1.5rem 0;
    }

    .hero h1 {
        font-size: 1.8rem;
    }

    .main-content {
        padding-bottom: 80px;
    }

    .mobile-bottom-nav {
        padding: env(safe-area-inset-bottom, 0) 0 0 0;
    }

    .bottom-nav-container {
        padding: 4px 0 8px 0;
    }
}

/* ===== SAFE AREA SUPPORT FOR NOTCHED DEVICES ===== */
@supports (padding: max(0px)) {
    .mobile-bottom-nav {
        padding-bottom: max(12px, env(safe-area-inset-bottom));
    }

    .main-content {
        padding-bottom: max(100px, calc(100px + env(safe-area-inset-bottom)));
    }
}
