// Configuration
const API_BASE_URL = 'http://localhost:5000/api'; // Local development URL

// Global variables
let adminToken = null;
let matches = [];
let filteredMatches = [];
let autoRefreshInterval = null;
let autoRefreshEnabled = true;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    checkAdminAuth();
    setupEventListeners();
    startAutoRefresh();
});

// Authentication (same as admin.js)
function checkAdminAuth() {
    const savedToken = localStorage.getItem('admin_token');
    if (savedToken) {
        adminToken = savedToken;
        showAdminContent();
        loadMatches();
    } else {
        showLoginModal();
    }
}

function showLoginModal() {
    document.getElementById('login-modal').style.display = 'block';
    document.getElementById('admin-content').style.display = 'none';
}

function showAdminContent() {
    document.getElementById('login-modal').style.display = 'none';
    document.getElementById('admin-content').style.display = 'block';
}

async function login() {
    const username = document.getElementById('admin-username').value.trim();
    const password = document.getElementById('admin-password').value.trim();
    const errorElement = document.getElementById('login-error');

    if (!username || !password) {
        showError(errorElement, 'Please enter username and password');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/admin/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        const data = await response.json();

        if (response.ok) {
            adminToken = data.token;
            localStorage.setItem('admin_token', data.token);
            showAdminContent();
            loadMatches();
            errorElement.style.display = 'none';
        } else {
            showError(errorElement, data.error || 'Invalid credentials');
        }
    } catch (error) {
        console.error('Login error:', error);
        showError(errorElement, 'Failed to connect to server');
    }
}

function logout() {
    adminToken = null;
    localStorage.removeItem('admin_token');
    showLoginModal();
    document.getElementById('admin-username').value = '';
    document.getElementById('admin-password').value = '';
    stopAutoRefresh();
}

function showError(element, message) {
    element.textContent = message;
    element.style.display = 'block';
}

// Match management
async function loadMatches() {
    const loadingElement = document.getElementById('loading-matches');
    const containerElement = document.getElementById('matches-container');
    const noMatchesElement = document.getElementById('no-matches');

    try {
        loadingElement.style.display = 'block';
        containerElement.innerHTML = '';
        noMatchesElement.style.display = 'none';

        const response = await fetch(`${API_BASE_URL}/admin/matches`, {
            headers: {
                'Authorization': `Bearer ${adminToken}`
            }
        });

        if (!response.ok) {
            throw new Error('Failed to load matches');
        }

        const data = await response.json();
        matches = data.matches || [];
        filteredMatches = [...matches];

        loadingElement.style.display = 'none';
        updateStats();
        applyCurrentFilter();

        if (filteredMatches.length === 0) {
            noMatchesElement.style.display = 'block';
        } else {
            displayMatches();
        }
    } catch (error) {
        console.error('Error loading matches:', error);
        loadingElement.style.display = 'none';
        containerElement.innerHTML = '<div class="error">Failed to load matches. Please try again later.</div>';
    }
}

function updateStats() {
    const totalMatches = matches.length;
    const activeMatches = matches.filter(m => m.status === 'open' || m.status === 'created').length;
    const finishedMatches = matches.filter(m => m.status === 'finished').length;

    document.getElementById('total-matches').textContent = totalMatches;
    document.getElementById('active-matches').textContent = activeMatches;
    document.getElementById('finished-matches').textContent = finishedMatches;
}

function applyCurrentFilter() {
    const statusFilter = document.getElementById('status-filter').value;
    
    if (statusFilter) {
        filteredMatches = matches.filter(match => match.status === statusFilter);
    } else {
        filteredMatches = [...matches];
    }
}

function displayMatches() {
    const container = document.getElementById('matches-container');
    container.innerHTML = '';

    // Sort matches by creation date (newest first)
    const sortedMatches = [...filteredMatches].sort((a, b) => 
        new Date(b.created_at) - new Date(a.created_at)
    );

    sortedMatches.forEach(match => {
        const matchCard = createDetailedMatchCard(match);
        container.appendChild(matchCard);
    });
}

function createDetailedMatchCard(match) {
    const card = document.createElement('div');
    card.className = 'match-card';
    
    const actionsHtml = getMatchActions(match);
    const predictionStats = getPredictionStats(match);
    
    card.innerHTML = `
        <div class="match-header">
            <div class="match-title">
                <h4>${match.teamA} vs ${match.teamB}</h4>
                <span class="match-id">#${match._id.slice(-6)}</span>
            </div>
            <span class="match-status status-${match.status}">${match.status.toUpperCase()}</span>
        </div>
        
        <div class="match-details">
            <div class="detail-row">
                <span class="label">Created:</span>
                <span class="value">${formatDate(match.created_at)}</span>
            </div>
            ${match.start_time ? `
                <div class="detail-row">
                    <span class="label">Started:</span>
                    <span class="value">${formatDate(match.start_time)}</span>
                </div>
            ` : ''}
            ${match.prediction_end_time ? `
                <div class="detail-row">
                    <span class="label">Predictions close:</span>
                    <span class="value">${formatDate(match.prediction_end_time)}</span>
                </div>
            ` : ''}
            <div class="detail-row">
                <span class="label">Prediction window:</span>
                <span class="value">${match.prediction_duration_minutes} minutes</span>
            </div>
            ${match.winner ? `
                <div class="detail-row">
                    <span class="label">Winner:</span>
                    <span class="value winner">${match.winner}</span>
                </div>
            ` : ''}
        </div>

        ${predictionStats}

        <div class="match-actions">
            ${actionsHtml}
        </div>
    `;

    return card;
}

function getPredictionStats(match) {
    if (match.total_predictions === undefined) return '';

    const teamAPercentage = match.total_predictions > 0 
        ? ((match.predictions_teamA / match.total_predictions) * 100).toFixed(1)
        : 0;
    const teamBPercentage = match.total_predictions > 0 
        ? ((match.predictions_teamB / match.total_predictions) * 100).toFixed(1)
        : 0;

    return `
        <div class="prediction-stats">
            <h5>Prediction Statistics</h5>
            <div class="stats-row">
                <span>Total Predictions: <strong>${match.total_predictions}</strong></span>
            </div>
            <div class="team-predictions">
                <div class="team-stat">
                    <span class="team-name">${match.teamA}:</span>
                    <span class="prediction-count">${match.predictions_teamA} (${teamAPercentage}%)</span>
                </div>
                <div class="team-stat">
                    <span class="team-name">${match.teamB}:</span>
                    <span class="prediction-count">${match.predictions_teamB} (${teamBPercentage}%)</span>
                </div>
            </div>
        </div>
    `;
}

function getMatchActions(match) {
    const actions = [];

    switch (match.status) {
        case 'created':
            actions.push(`<button class="btn btn-success" onclick="startPrediction('${match._id}')">
                <i class="fas fa-play"></i> Start Predictions
            </button>`);
            break;
        case 'open':
            actions.push(`<button class="btn btn-warning" onclick="closePrediction('${match._id}')">
                <i class="fas fa-stop"></i> Close Predictions
            </button>`);
            break;
        case 'closed':
            actions.push(`<button class="btn btn-primary" onclick="setWinner('${match._id}', '${match.teamA}')">
                ${match.teamA} Wins
            </button>`);
            actions.push(`<button class="btn btn-primary" onclick="setWinner('${match._id}', '${match.teamB}')">
                ${match.teamB} Wins
            </button>`);
            break;
        case 'finished':
            actions.push(`<span class="btn btn-secondary" disabled>
                <i class="fas fa-check"></i> Match Completed
            </span>`);
            break;
    }

    return actions.join('');
}

// Match actions (same as admin.js)
async function startPrediction(matchId) {
    if (!confirm('Start prediction window for this match?')) return;

    try {
        const response = await fetch(`${API_BASE_URL}/admin/start_prediction`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({ match_id: matchId })
        });

        const data = await response.json();

        if (response.ok) {
            alert('Prediction started successfully!');
            loadMatches();
        } else {
            alert(data.error || 'Failed to start prediction');
        }
    } catch (error) {
        console.error('Error starting prediction:', error);
        alert('Failed to start prediction. Please try again.');
    }
}

async function closePrediction(matchId) {
    if (!confirm('Close prediction window for this match?')) return;

    try {
        const response = await fetch(`${API_BASE_URL}/admin/close_prediction`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({ match_id: matchId })
        });

        const data = await response.json();

        if (response.ok) {
            alert('Prediction closed successfully!');
            loadMatches();
        } else {
            alert(data.error || 'Failed to close prediction');
        }
    } catch (error) {
        console.error('Error closing prediction:', error);
        alert('Failed to close prediction. Please try again.');
    }
}

async function setWinner(matchId, winnerTeam) {
    if (!confirm(`Set ${winnerTeam} as the winner? This will award points to users who predicted correctly.`)) return;

    try {
        const response = await fetch(`${API_BASE_URL}/admin/set_winner`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({
                match_id: matchId,
                winner_team: winnerTeam
            })
        });

        const data = await response.json();

        if (response.ok) {
            alert(`Winner set successfully! Points awarded to ${data.points_awarded_to} users.`);
            loadMatches();
        } else {
            alert(data.error || 'Failed to set winner');
        }
    } catch (error) {
        console.error('Error setting winner:', error);
        alert('Failed to set winner. Please try again.');
    }
}

// Auto-refresh functionality
function startAutoRefresh() {
    if (autoRefreshEnabled) {
        autoRefreshInterval = setInterval(() => {
            if (adminToken) {
                loadMatches();
            }
        }, 30000); // Refresh every 30 seconds
    }
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

function toggleAutoRefresh() {
    autoRefreshEnabled = !autoRefreshEnabled;
    const button = document.getElementById('auto-refresh-toggle');
    
    if (autoRefreshEnabled) {
        button.innerHTML = '<i class="fas fa-pause"></i> Auto-refresh: ON';
        button.className = 'btn btn-secondary btn-sm';
        startAutoRefresh();
    } else {
        button.innerHTML = '<i class="fas fa-play"></i> Auto-refresh: OFF';
        button.className = 'btn btn-warning btn-sm';
        stopAutoRefresh();
    }
}

// Event listeners
function setupEventListeners() {
    // Login
    document.getElementById('login-btn').addEventListener('click', login);
    document.getElementById('admin-username').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('admin-password').focus();
        }
    });
    document.getElementById('admin-password').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            login();
        }
    });

    // Logout
    document.getElementById('logout-btn').addEventListener('click', logout);

    // Filters
    document.getElementById('apply-filter').addEventListener('click', function() {
        applyCurrentFilter();
        displayMatches();
    });

    document.getElementById('clear-filter').addEventListener('click', function() {
        document.getElementById('status-filter').value = '';
        applyCurrentFilter();
        displayMatches();
    });

    // Refresh controls
    document.getElementById('refresh-matches').addEventListener('click', loadMatches);
    document.getElementById('auto-refresh-toggle').addEventListener('click', toggleAutoRefresh);
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString();
}
