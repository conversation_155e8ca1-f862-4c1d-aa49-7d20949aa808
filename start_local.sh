#!/bin/bash

echo "========================================"
echo " Valorant Tournament Prediction Website"
echo " Local Development Server"
echo "========================================"
echo

echo "[1/3] Installing Python dependencies..."
cd backend
pip install -r requirements.txt
echo

echo "[2/3] Starting Flask backend server..."
echo "Backend will run on: http://localhost:5000"
echo "Admin credentials: username=admin, password=admin123"
echo
python app.py &
BACKEND_PID=$!

echo "[3/3] Opening frontend in browser..."
sleep 3
cd ../frontend

# Try to open in browser (works on most systems)
if command -v xdg-open > /dev/null; then
    xdg-open index.html
elif command -v open > /dev/null; then
    open index.html
else
    echo "Please open frontend/index.html in your browser"
fi

echo
echo "========================================"
echo " Setup Complete!"
echo "========================================"
echo " Frontend: Open frontend/index.html in browser"
echo " Admin Panel: Open admin/index.html in browser"
echo " Backend API: http://localhost:5000"
echo " Admin Login: username=admin, password=admin123"
echo "========================================"
echo " Press Ctrl+C to stop the backend server"
echo "========================================"

# Wait for backend process
wait $BACKEND_PID
