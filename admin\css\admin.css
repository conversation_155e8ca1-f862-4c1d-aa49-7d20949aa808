/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background-color: #0F1419;
    color: #FFFFFF;
    line-height: 1.6;
    overflow-x: hidden;
}

/* ===== VARIABLES ===== */
:root {
    --primary-red: #FF4655;
    --dark-bg: #0F1419;
    --card-bg: #1E2328;
    --border-color: #3C3C41;
    --text-primary: #FFFFFF;
    --text-secondary: #AAABAD;
    --accent-blue: #00D4FF;
    --success-green: #00F5A0;
    --warning-yellow: #FFCC02;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.accent {
    color: var(--primary-red);
}

/* ===== LAYOUT ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== NAVIGATION ===== */
.navbar {
    background: var(--dark-bg);
    border-bottom: 2px solid var(--primary-red);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
}

.nav-logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
}

.nav-logo .accent {
    color: var(--primary-red);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-red);
    background: rgba(255, 70, 85, 0.1);
}

.admin-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Buttons */
.btn {
    padding: 1rem 2rem;
    border: 2px solid #ff4655;
    background: linear-gradient(135deg, rgba(255, 70, 85, 0.1), rgba(255, 70, 85, 0.05));
    color: #ff4655;
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    position: relative;
    clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 100%, 15px 100%);
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 70, 85, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-sm {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #ff4655, #ff6b7a);
    color: #0f1419;
    border-color: #ff4655;
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #ff6b7a, #ff4655);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 70, 85, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #666, #888);
    color: #ece8e1;
    border-color: #666;
    box-shadow: 0 4px 15px rgba(102, 102, 102, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #888, #666);
    transform: translateY(-3px);
}

.btn-success {
    background: linear-gradient(135deg, #00d4aa, #00f5d4);
    color: #0f1419;
    border-color: #00d4aa;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #00f5d4, #00d4aa);
    transform: translateY(-3px);
}

.btn-danger {
    background: linear-gradient(135deg, #ff4655, #ff1744);
    color: #ece8e1;
    border-color: #ff4655;
    box-shadow: 0 4px 15px rgba(255, 70, 85, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ff1744, #ff4655);
    transform: translateY(-3px);
}

.btn-warning {
    background: linear-gradient(135deg, #ff9800, #ffb74d);
    color: #0f1419;
    border-color: #ff9800;
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #ffb74d, #ff9800);
    transform: translateY(-3px);
}

.btn-info {
    background: linear-gradient(135deg, #2196f3, #64b5f6);
    color: #0f1419;
    border-color: #2196f3;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.btn-info:hover {
    background: linear-gradient(135deg, #64b5f6, #2196f3);
    transform: translateY(-3px);
}

/* ===== MAIN CONTENT ===== */
.main-content {
    margin-top: 70px;
    min-height: calc(100vh - 70px);
    background: var(--dark-bg);
    padding: 2rem 0;
}

/* ===== DASHBOARD HEADER ===== */
.dashboard-header {
    text-align: center;
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--dark-bg) 0%, var(--card-bg) 100%);
    position: relative;
    margin-bottom: 3rem;
}

.dashboard-header h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 2px;
}

.dashboard-header h1 .accent {
    color: var(--primary-red);
}

.dashboard-header p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Dashboard grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.dashboard-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    position: relative;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-red);
}

.dashboard-card.full-width {
    grid-column: 1 / -1;
}

.card-header {
    background: var(--dark-bg);
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.card-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.card-header i {
    margin-right: 0.75rem;
    color: var(--primary-red);
}

.card-body {
    padding: 2rem;
    color: var(--text-primary);
}

/* ===== FORMS ===== */
.input-group {
    margin-bottom: 1.5rem;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    background: var(--dark-bg);
    color: var(--text-primary);
    font-family: 'Rajdhani', sans-serif;
    transition: border-color 0.3s ease;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: var(--primary-red);
}

.input-group input::placeholder {
    color: var(--text-secondary);
}

/* Quick actions */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* ===== MATCH CARDS ===== */
.match-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-red);
}

.match-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-red);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.match-id {
    font-family: 'Courier New', monospace;
    background: var(--dark-bg);
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    border: 1px solid var(--border-color);
}

.match-status {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: 1px solid;
}

.status-created {
    background: rgba(156, 39, 176, 0.2);
    color: #ba68c8;
    border-color: #ba68c8;
}

.status-open {
    background: rgba(0, 245, 160, 0.2);
    color: var(--success-green);
    border-color: var(--success-green);
    animation: pulse 2s infinite;
}

.status-closed {
    background: rgba(255, 204, 2, 0.2);
    color: var(--warning-yellow);
    border-color: var(--warning-yellow);
}

.status-finished {
    background: rgba(0, 212, 255, 0.2);
    color: var(--accent-blue);
    border-color: var(--accent-blue);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* ===== USERS TABLE ===== */
.users-table {
    width: 100%;
}

.users-table .table-header,
.users-table .table-row {
    display: grid;
    grid-template-columns: 100px 1fr 1fr 1.5fr 80px 80px 120px 100px;
    gap: 1rem;
    padding: 1rem;
    align-items: center;
}

.users-table .table-header {
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.users-table .table-row {
    background: rgba(30, 35, 40, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    color: var(--text-primary);
    font-weight: 500;
}

.users-table .table-row:hover {
    background: rgba(255, 70, 85, 0.1);
    border-color: var(--primary-red);
    transform: translateX(3px);
}

.user-id {
    font-family: 'Courier New', monospace;
    background: var(--dark-bg);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    border: 1px solid var(--border-color);
}

.full-name {
    font-weight: 600;
}

.in-game-name {
    color: var(--primary-red);
    font-weight: 600;
}

.email {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.points strong {
    color: var(--primary-red);
    font-size: 1.1rem;
}

.join-date {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.match-teams {
    text-align: center;
    margin: 1rem 0;
}

.match-teams h4 {
    font-size: 1.2rem;
    color: #333;
}

.match-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.match-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* ===== MODAL ===== */
.modal {
    display: block;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    margin: 5% auto;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    overflow: hidden;
}

.modal-header {
    background: var(--dark-bg);
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    position: relative;
}

.modal-header h2 {
    color: var(--text-primary);
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.modal-body {
    padding: 2rem;
    color: var(--text-primary);
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}



/* Loading and error states */
.loading {
    text-align: center;
    color: #666;
    font-size: 1.1rem;
    padding: 2rem;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

.no-data {
    text-align: center;
    color: #666;
    padding: 2rem;
}

.no-data i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.error {
    background: #f8d7da;
    color: #721c24;
    padding: 0.75rem;
    border-radius: 8px;
    margin-top: 1rem;
}

.success {
    background: #d4edda;
    color: #155724;
    padding: 0.75rem;
    border-radius: 8px;
    margin-top: 1rem;
}

/* Footer */
footer {
    background: rgba(0, 0, 0, 0.1);
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: auto;
}

/* Additional match card styles */
.match-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.match-title h4 {
    margin: 0;
    color: #333;
}

.match-details {
    margin: 1rem 0;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    font-weight: 600;
    color: #666;
}

.detail-row .value {
    color: #333;
}

.detail-row .value.winner {
    color: #27ae60;
    font-weight: bold;
}

.prediction-stats {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.prediction-stats h5 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.stats-row {
    margin-bottom: 0.5rem;
}

.team-predictions {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.team-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.team-name {
    font-weight: 600;
}

.prediction-count {
    color: #3498db;
    font-weight: bold;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    text-align: center;
}

.stat-item {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-menu {
        gap: 1rem;
    }

    .dashboard-header h1 {
        font-size: 2rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .match-actions {
        flex-direction: column;
    }

    .match-actions .btn {
        flex: none;
    }

    .admin-info {
        flex-direction: column;
        gap: 0.5rem;
    }

    .match-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .team-predictions {
        font-size: 0.9rem;
    }
}
