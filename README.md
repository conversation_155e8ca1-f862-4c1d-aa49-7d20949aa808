# 🎮 Valorant Tournament Prediction Website

A complete tournament prediction platform with user-facing frontend, admin panel, and Python backend.

## 📦 Project Structure

```
match-predict/
├── backend/                 # Python Flask Backend
│   ├── app.py              # Main Flask application
│   ├── models.py           # Database models and MongoDB connection
│   ├── requirements.txt    # Python dependencies
│   └── render.yaml         # Render.com deployment config
├── frontend/               # User-facing Static Site
│   ├── index.html          # Home page with active matches
│   ├── leaderboard.html    # Global leaderboard
│   ├── profile.html        # User stats and prediction history
│   ├── css/
│   │   └── style.css       # Main stylesheet
│   └── js/
│       ├── main.js         # Home page functionality
│       ├── leaderboard.js  # Leaderboard functionality
│       └── profile.js      # Profile page functionality
├── admin/                  # Admin Panel
│   ├── index.html          # Admin dashboard
│   ├── matches.html        # Match management
│   ├── stats.html          # Statistics overview
│   ├── css/
│   │   └── admin.css       # Admin panel styling
│   └── js/
│       └── admin.js        # Admin functionality
└── README.md
```

## 🚀 Quick Start

### 1. Backend Setup (Render.com)

1. **Create MongoDB Atlas Database:**
   - Sign up at [MongoDB Atlas](https://www.mongodb.com/atlas)
   - Create a new cluster
   - Get your connection string

2. **Deploy to Render.com:**
   - Push this repository to GitHub
   - Connect your GitHub repo to Render.com
   - Create a new Web Service
   - Set the build command: `pip install -r requirements.txt`
   - Set the start command: `gunicorn app:app`
   - Add environment variables:
     - `MONGODB_URI`: Your MongoDB Atlas connection string
     - `DB_NAME`: `valorant_predictions`
     - `ADMIN_TOKEN`: Generate a secure token for admin access
     - `SECRET_KEY`: Generate a secure secret key

3. **Your backend will be available at:** `https://your-app-name.onrender.com`

### 2. Frontend Setup (Static Hosting)

1. **Update API URLs:**
   - Edit `frontend/js/main.js`
   - Edit `frontend/js/leaderboard.js`
   - Edit `frontend/js/profile.js`
   - Edit `admin/js/admin.js`
   - Replace `https://your-backend-url.onrender.com/api` with your actual backend URL

2. **Deploy Frontend:**
   - Upload the `frontend/` folder to any static hosting service:
     - Netlify
     - Vercel
     - GitHub Pages
     - Firebase Hosting

3. **Deploy Admin Panel:**
   - Upload the `admin/` folder to static hosting
   - Keep the admin URL private/secure

## 🔧 Configuration

### Backend Environment Variables

```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/
DB_NAME=valorant_predictions
ADMIN_TOKEN=your-secure-admin-token-here
SECRET_KEY=your-secret-key-here
PORT=5000
```

### Frontend Configuration

Update the `API_BASE_URL` in all JavaScript files:

```javascript
const API_BASE_URL = 'https://your-backend-url.onrender.com/api';
```

## 📱 Features

### User Features
- **Match Predictions:** Users can predict winners of active matches
- **Real-time Countdown:** Shows time remaining for predictions
- **Leaderboard:** Global ranking by points earned
- **Profile Page:** Personal stats and prediction history
- **Responsive Design:** Works on desktop and mobile

### Admin Features
- **Match Management:** Create, start, and close prediction windows
- **Winner Selection:** Set match winners and award points automatically
- **Real-time Control:** Start/stop predictions with countdown timers
- **Statistics Dashboard:** View match and user statistics

## 🎯 How It Works

1. **Admin creates a match** with two teams and prediction duration
2. **Admin starts predictions** - countdown timer begins
3. **Users make predictions** during the open window
4. **Admin closes predictions** when the match starts
5. **Admin sets the winner** after the match ends
6. **Points are automatically awarded** to users with correct predictions

## 🔒 Security

- **Admin Authentication:** Token-based authentication for admin panel
- **CORS Enabled:** Allows frontend to communicate with backend
- **Input Validation:** Server-side validation for all inputs
- **Error Handling:** Comprehensive error handling and user feedback

## 🛠️ Development

### Local Backend Development

```bash
cd backend
pip install -r requirements.txt
export MONGODB_URI="your-mongodb-uri"
export ADMIN_TOKEN="test-token"
export SECRET_KEY="dev-secret"
python app.py
```

### Local Frontend Development

Simply open the HTML files in a browser or use a local server:

```bash
cd frontend
python -m http.server 8000
# Visit http://localhost:8000
```

## 📊 Database Schema

### Collections

**matches:**
```json
{
  "_id": "ObjectId",
  "teamA": "Team Name A",
  "teamB": "Team Name B",
  "status": "created|open|closed|finished",
  "start_time": "DateTime",
  "prediction_end_time": "DateTime",
  "winner": "Team Name",
  "prediction_duration_minutes": 5,
  "total_predictions": 0,
  "predictions_teamA": 0,
  "predictions_teamB": 0
}
```

**predictions:**
```json
{
  "_id": "ObjectId",
  "user_id": "user_123",
  "match_id": "match_id",
  "selected_team": "Team Name",
  "timestamp": "DateTime",
  "is_correct": true|false|null
}
```

**users:**
```json
{
  "_id": "user_123",
  "points": 10,
  "total_predictions": 5,
  "correct_predictions": 3,
  "created_at": "DateTime"
}
```

## 🚀 Deployment Checklist

- [ ] MongoDB Atlas database created
- [ ] Backend deployed to Render.com
- [ ] Environment variables configured
- [ ] Frontend API URLs updated
- [ ] Frontend deployed to static hosting
- [ ] Admin panel deployed separately
- [ ] Admin token secured
- [ ] Test all functionality

## 🎮 Usage

1. **Admin:** Access admin panel, create matches, manage predictions
2. **Users:** Visit frontend, make predictions, check leaderboard
3. **Real-time:** Countdown timers and auto-refresh keep everything synchronized

## 🔧 Customization

- **Styling:** Modify CSS files for custom branding
- **Points System:** Adjust point values in `models.py`
- **Prediction Duration:** Change default duration in admin panel
- **Teams:** Add team logos and additional match information

## 📞 Support

For issues or questions:
1. Check the browser console for JavaScript errors
2. Check Render.com logs for backend errors
3. Verify MongoDB Atlas connection
4. Ensure all environment variables are set correctly

---

**Built with:** Python Flask, MongoDB Atlas, HTML/CSS/JavaScript, Render.com
