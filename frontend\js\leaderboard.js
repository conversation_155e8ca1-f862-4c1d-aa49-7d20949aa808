// Configuration
const API_BASE_URL = 'http://localhost:5000/api';

// Global variables
let currentUser = null;
let leaderboardData = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeUser();
    loadLeaderboard();
    setupEventListeners();
});

// User management
function initializeUser() {
    const savedUser = localStorage.getItem('valorant_user');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        updateUserDisplay();
        loadUserStats(); // Load fresh stats from server
    }
}

function updateUserDisplay() {
    console.log('Leaderboard: updateUserDisplay called with currentUser:', currentUser);

    if (currentUser) {
        const pointsText = `Points: ${currentUser.points}`;
        const userText = currentUser.in_game_name || currentUser.full_name;

        console.log('Leaderboard: Setting points text to:', pointsText);
        console.log('Leaderboard: Setting user text to:', userText);

        document.getElementById('user-points').textContent = pointsText;
        document.getElementById('user-id-display').textContent = userText;

        // Show logout button
        const logoutBtn = document.getElementById('nav-logout-btn');
        if (logoutBtn) {
            logoutBtn.style.display = 'inline-block';
        }
    } else {
        // Hide logout button
        const logoutBtn = document.getElementById('nav-logout-btn');
        if (logoutBtn) {
            logoutBtn.style.display = 'none';
        }
    }
}

// Load user statistics
async function loadUserStats() {
    if (!currentUser) {
        console.log('Leaderboard: No current user, skipping stats load');
        return;
    }

    try {
        console.log(`Leaderboard: Loading stats for user: ${currentUser.id}`);

        const response = await fetch(`${API_BASE_URL}/user/${currentUser.id}`);
        console.log('Leaderboard: User stats response status:', response.status);

        if (response.ok) {
            const data = await response.json();
            console.log('Leaderboard: User stats response data:', data);

            if (data.user) {
                const oldPoints = currentUser.points;
                currentUser.points = data.user.points;
                console.log(`Leaderboard: User points updated: ${oldPoints} -> ${currentUser.points}`);

                updateUserDisplay();
                // Update localStorage
                localStorage.setItem('valorant_user', JSON.stringify(currentUser));
                console.log('Leaderboard: User display updated and localStorage saved');
            } else {
                console.error('Leaderboard: No user data in response:', data);
            }
        } else {
            const errorText = await response.text();
            console.error('Leaderboard: Failed to load user stats:', response.status, errorText);
        }
    } catch (error) {
        console.error('Leaderboard: Error loading user stats:', error);
    }
}

// Leaderboard management
async function loadLeaderboard() {
    const loadingElement = document.getElementById('loading');
    const containerElement = document.getElementById('leaderboard-container');
    const noDataElement = document.getElementById('no-data');

    try {
        loadingElement.style.display = 'block';
        containerElement.style.display = 'none';
        noDataElement.style.display = 'none';

        const response = await fetch(`${API_BASE_URL}/leaderboard`);
        if (!response.ok) {
            throw new Error('Failed to load leaderboard');
        }

        const data = await response.json();
        leaderboardData = data.leaderboard || [];

        loadingElement.style.display = 'none';

        if (leaderboardData.length === 0) {
            noDataElement.style.display = 'block';
        } else {
            containerElement.style.display = 'block';
            displayLeaderboard();
        }
    } catch (error) {
        console.error('Error loading leaderboard:', error);
        loadingElement.style.display = 'none';
        containerElement.innerHTML = '<div class="error">Failed to load leaderboard. Please try again later.</div>';
    }
}

function displayLeaderboard() {
    const listElement = document.getElementById('leaderboard-list');
    listElement.innerHTML = '';

    leaderboardData.forEach((user, index) => {
        const entry = createLeaderboardEntry(user, index + 1);
        listElement.appendChild(entry);
    });
}

function createLeaderboardEntry(user, rank) {
    const entry = document.createElement('div');
    entry.className = 'table-row';
    
    // Highlight current user
    if (currentUser && user._id === currentUser.id) {
        entry.classList.add('current-user');
    }

    // Add special styling for top 3
    if (rank <= 3) {
        entry.classList.add(`rank-${rank}`);
    }

    const rankIcon = getRankIcon(rank);
    const accuracy = user.total_predictions > 0 ? user.accuracy.toFixed(1) : '0.0';

    entry.innerHTML = `
        <div class="rank">
            ${rankIcon}
            <span class="rank-number">${rank}</span>
        </div>
        <div class="username">
            ${user.display_name || user.in_game_name || user.full_name || `User ${user._id.slice(-8)}`}
            ${currentUser && user._id === currentUser.id ? '<span class="you-badge">YOU</span>' : ''}
        </div>
        <div class="points">
            <strong>${user.points}</strong>
        </div>
        <div class="predictions">
            ${user.total_predictions}
        </div>
        <div class="accuracy">
            ${accuracy}%
        </div>
    `;

    return entry;
}

function getRankIcon(rank) {
    switch (rank) {
        case 1:
            return '<i class="fas fa-crown gold"></i>';
        case 2:
            return '<i class="fas fa-medal silver"></i>';
        case 3:
            return '<i class="fas fa-medal bronze"></i>';
        default:
            return '';
    }
}

// Logout functionality
function logout() {
    if (confirm('Are you sure you want to logout?')) {
        localStorage.removeItem('valorant_user');
        currentUser = null;
        updateUserDisplay();
        alert('You have been logged out successfully!');
        window.location.href = 'index.html';
    }
}

// Event listeners
function setupEventListeners() {
    document.getElementById('refresh-btn').addEventListener('click', loadLeaderboard);

    // Logout button
    const navLogoutBtn = document.getElementById('nav-logout-btn');
    if (navLogoutBtn) {
        navLogoutBtn.addEventListener('click', logout);
    }

    // Mobile app-style navigation
    setupMobileNavigation();

    // Auto-refresh every 60 seconds
    setInterval(() => {
        loadLeaderboard();
        if (currentUser) {
            loadUserStats(); // Also refresh user stats
        }
    }, 60000);
}

// Mobile app-style navigation functions
function setupMobileNavigation() {
    const bottomNavItems = document.querySelectorAll('.bottom-nav-item');

    bottomNavItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Add touch feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);

            // Update active state for current page
            if (this.getAttribute('href')) {
                updateActiveNavItem(this);
            }
        });
    });
}

function updateActiveNavItem(activeItem) {
    const bottomNavItems = document.querySelectorAll('.bottom-nav-item');
    bottomNavItems.forEach(item => {
        item.classList.remove('active');
    });
    activeItem.classList.add('active');
}
