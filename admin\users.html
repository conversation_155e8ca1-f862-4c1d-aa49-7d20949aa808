<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Valorant Predictions Admin</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h1>ADMIN <span class="accent">CONTROL</span></h1>
                </div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a href="matches.html" class="nav-link">Matches</a>
                    </li>
                    <li class="nav-item">
                        <a href="users.html" class="nav-link active">Users</a>
                    </li>
                    <li class="nav-item">
                        <a href="stats.html" class="nav-link">Statistics</a>
                    </li>
                </ul>
                <div class="admin-info">
                    <span id="admin-status">Admin Panel</span>
                    <button id="logout-btn" class="btn btn-danger btn-sm">Logout</button>
                </div>
            </div>
        </nav>

        <main class="main-content">
            <div class="container">
                <section class="dashboard-header">
                    <h1>USER <span class="accent">MANAGEMENT</span></h1>
                    <p>Monitor and manage registered users</p>
                </section>

                <section class="users-section">
                    <div class="dashboard-card full-width">
                        <div class="card-header">
                            <h3><i class="fas fa-users"></i> Registered Users</h3>
                            <button id="refresh-users-btn" class="btn btn-secondary">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="loading-users" class="loading">
                                <i class="fas fa-spinner"></i>
                                <p>Loading users...</p>
                            </div>

                            <div id="users-container" style="display: none;">
                                <div class="users-table">
                                    <div class="table-header">
                                        <div>ID</div>
                                        <div>Full Name</div>
                                        <div>In-Game Name</div>
                                        <div>Email</div>
                                        <div>Points</div>
                                        <div>Predictions</div>
                                        <div>Joined</div>
                                        <div>Status</div>
                                    </div>
                                    <div id="users-list"></div>
                                </div>
                            </div>

                            <div id="no-users" class="no-matches" style="display: none;">
                                <i class="fas fa-user-slash"></i>
                                <h3>No Users Found</h3>
                                <p>No registered users in the system yet.</p>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Login Modal -->
    <div id="login-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Admin Login Required</h2>
            </div>
            <div class="modal-body">
                <form class="login-form">
                    <div class="input-group">
                        <label for="admin-username">Username:</label>
                        <input type="text" id="admin-username" required>
                    </div>
                    <div class="input-group">
                        <label for="admin-password">Password:</label>
                        <input type="password" id="admin-password" required>
                    </div>
                    <div class="input-group">
                        <button type="submit" id="login-submit" class="btn btn-primary">Login</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/users.js"></script>
</body>
</html>
